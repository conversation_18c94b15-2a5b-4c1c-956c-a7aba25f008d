import plugin from '../../../lib/plugins/plugin.js'
import GachaService from '../models/GachaService.js'
import PoolManager from '../models/PoolManager.js'
import { renderGachaResult } from '../utils/render.js' // 确保这里的路径正确

export class WutheringGacha extends plugin {
  constructor() {
    super({
      name: '鸣潮抽卡',
      dsc: '鸣潮模拟抽卡功能',
      event: 'message',
      priority: 5000,
      rule: [
        {
          reg: '^#?ww抽卡up(十连|单抽)$',
          fnc: 'drawLimitedChar'
        },
        {
          reg: '^#?ww抽卡up武器(十连|单抽)$',
          fnc: 'drawLimitedWeapon'
        },
        {
          reg: '^#?ww抽卡常驻(十连|单抽)$',
          fnc: 'drawStandardChar'
        },
        {
          reg: '^#?ww抽卡常驻武器(十连|单抽)$',
          fnc: 'drawStandardWeapon'
        }
      ]
    })
    
    this.gachaService = new GachaService()
    this.poolManager = new PoolManager()
  }

  async drawLimitedChar() {
    const userId = this.e.user_id
    const drawType = this.e.msg.includes('十连') ? 10 : 1
    
    try {
      const availablePools = await this.poolManager.getActiveLimitedCharPools()
      
      if (!availablePools || availablePools.length === 0) {
        await this.reply('当前没有可用的限定角色池，请等待卡池更新')
        return
      }
      
      let targetPool
      
      if (availablePools.length === 1) {
        targetPool = availablePools[0]
      } else {
        const selectedPool = await this.gachaService.getSelectedPool(userId, 'limited_char')
        
        if (!selectedPool) {
          let msg = '请先选择要抽取的限定角色池：\n\n'
          availablePools.forEach((pool, index) => {
            msg += `${index + 1}. ${pool.name}\n`
            msg += `   UP角色：${pool.up['5star'][0].name}\n`
          })
          msg += '\n请使用【#ww切换卡池】命令选择'
          await this.reply(msg)
          return
        }
        
        targetPool = selectedPool
      }
      
      const canDraw = await this.gachaService.checkDrawLimit(userId, targetPool.id, this.e)
      if (!canDraw) {
        return
      }
      
      const results = await this.gachaService.performDraw(userId, targetPool, drawType)
      
      // 获取当前抽数
      const currentPity = await this.gachaService.getCurrentDrawCount(userId, targetPool.id)

      // 生成用户特征码
      const userCode = await this.gachaService.getUserCode(userId)
      
      // 传递 userCode 和 currentPity 到渲染函数
      const imgData = await renderGachaResult(results, targetPool, drawType, userCode, currentPity) 
      await this.reply(imgData)
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 限定角色池抽卡失败：${error}`)
      console.error(error.stack)
      await this.reply('抽卡失败，请稍后再试')
    }
  }

  async drawLimitedWeapon() {
    const userId = this.e.user_id
    const drawType = this.e.msg.includes('十连') ? 10 : 1
    
    try {
      const availablePools = await this.poolManager.getActiveLimitedWeaponPools()
      
      if (!availablePools || availablePools.length === 0) {
        await this.reply('当前没有可用的限定武器池，请等待卡池更新')
        return
      }
      
      let targetPool
      
      if (availablePools.length === 1) {
        targetPool = availablePools[0]
      } else {
        const selectedPool = await this.gachaService.getSelectedPool(userId, 'limited_weapon')
        
        if (!selectedPool) {
          let msg = '请先选择要抽取的限定武器池：\n\n'
          availablePools.forEach((pool, index) => {
            msg += `${index + 1}. ${pool.name}\n`
            msg += `   UP武器：${pool.up['5star'][0].name}\n`
          })
          msg += '\n请使用【#ww切换卡池】命令选择'
          await this.reply(msg)
          return
        }
        
        targetPool = selectedPool
      }
      
      const canDraw = await this.gachaService.checkDrawLimit(userId, targetPool.id, this.e)
      if (!canDraw) {
        return
      }
      
      const results = await this.gachaService.performDraw(userId, targetPool, drawType)
      
      // 获取当前抽数
      const currentPity = await this.gachaService.getCurrentDrawCount(userId, targetPool.id)

      // 生成用户特征码
      const userCode = await this.gachaService.getUserCode(userId)
      
      // 传递 userCode 和 currentPity 到渲染函数
      const imgData = await renderGachaResult(results, targetPool, drawType, userCode, currentPity)
      await this.reply(imgData)
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 限定武器池抽卡失败：${error}`)
      await this.reply('抽卡失败，请稍后再试')
    }
  }

  async drawStandardChar() {
    const userId = this.e.user_id
    const drawType = this.e.msg.includes('十连') ? 10 : 1
    
    try {
      const pool = await this.poolManager.getStandardCharPool()
      if (!pool) {
        await this.reply('常驻角色池配置错误，请联系管理员')
        return
      }
      
      const canDraw = await this.gachaService.checkDrawLimit(userId, 'standard_char', this.e)
      if (!canDraw) {
        return
      }
      
      const results = await this.gachaService.performDraw(userId, pool, drawType)
      
      // 获取当前抽数
      const currentPity = await this.gachaService.getCurrentDrawCount(userId, 'standard_char')

      // 生成用户特征码
      const userCode = await this.gachaService.getUserCode(userId)
      
      // 传递 userCode 和 currentPity 到渲染函数
      const imgData = await renderGachaResult(results, pool, drawType, userCode, currentPity)
      await this.reply(imgData)
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 常驻角色池抽卡失败：${error}`)
      await this.reply('抽卡失败，请稍后再试')
    }
  }

  async drawStandardWeapon() {
    const userId = this.e.user_id
    const drawType = this.e.msg.includes('十连') ? 10 : 1
    
    try {
      const targetWeapon = await this.gachaService.getTargetWeapon(userId)
      if (!targetWeapon) {
        await this.reply('请先使用【#ww定向武器】命令设置要定向获取的5星武器！')
        return
      }
      
      const pool = await this.poolManager.getStandardWeaponPool()
      if (!pool) {
        await this.reply('常驻武器池配置错误，请联系管理员')
        return
      }
      
      pool.targetWeapon = targetWeapon
      
      const canDraw = await this.gachaService.checkDrawLimit(userId, 'standard_weapon', this.e)
      if (!canDraw) {
        return
      }
      
      const results = await this.gachaService.performDraw(userId, pool, drawType)
      
      // 获取当前抽数
      const currentPity = await this.gachaService.getCurrentDrawCount(userId, 'standard_weapon')

      // 生成用户特征码
      const userCode = await this.gachaService.getUserCode(userId)
      
      // 传递 userCode 和 currentPity 到渲染函数
      const imgData = await renderGachaResult(results, pool, drawType, userCode, currentPity)
      await this.reply(imgData)
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 常驻武器池抽卡失败：${error}`)
      await this.reply('抽卡失败，请稍后再试')
    }
  }

  getPoolType(poolId) {
    if (poolId.startsWith('limited_char')) return 'limited_char'
    if (poolId.startsWith('limited_weapon')) return 'limited_weapon'
    if (poolId === 'standard_char') return 'standard_char'
    if (poolId === 'standard_weapon') return 'standard_weapon'
    return 'unknown'
  }
}

export default WutheringGacha
