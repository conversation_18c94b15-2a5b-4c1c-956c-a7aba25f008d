import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import DatabaseManager from '../models/DatabaseManager.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

async function updateUserDataFiles() {
  console.log('开始更新用户数据文件...')
  
  try {
    // 初始化数据库
    const dbManager = DatabaseManager.getInstance()
    await dbManager.init()
    
    const dataDir = path.join(__dirname, '..', 'data')
    
    // 获取所有用户文件夹
    const userDirs = fs.readdirSync(dataDir).filter(item => {
      const fullPath = path.join(dataDir, item)
      return fs.statSync(fullPath).isDirectory() && /^\d+$/.test(item) && item !== 'db'
    })
    
    console.log(`找到 ${userDirs.length} 个用户文件夹`)
    
    for (const userCode of userDirs) {
      const userDataPath = path.join(dataDir, userCode, 'user_data.json')
      
      if (fs.existsSync(userDataPath)) {
        try {
          // 读取现有数据
          const userData = JSON.parse(fs.readFileSync(userDataPath, 'utf8'))
          
          // 从数据库获取userId
          const userId = await dbManager.getUserIdByCode(parseInt(userCode))
          
          if (userId && userData.userId !== userId) {
            // 更新userId
            userData.userId = userId
            userData.lastUpdated = Date.now()
            
            // 保存更新后的数据
            fs.writeFileSync(userDataPath, JSON.stringify(userData, null, 2), 'utf8')
            console.log(`✅ 更新用户 ${userCode} 的userId: ${userId}`)
          } else if (!userId) {
            console.log(`⚠️  用户 ${userCode} 在数据库中未找到对应的userId`)
          } else {
            console.log(`ℹ️  用户 ${userCode} 的userId已是最新: ${userId}`)
          }
        } catch (error) {
          console.error(`❌ 处理用户 ${userCode} 时出错:`, error)
        }
      } else {
        console.log(`⚠️  用户 ${userCode} 的user_data.json文件不存在`)
      }
    }
    
    console.log('用户数据文件更新完成！')
    
  } catch (error) {
    console.error('更新用户数据文件时出错:', error)
  }
}

// 运行更新脚本
updateUserDataFiles()
