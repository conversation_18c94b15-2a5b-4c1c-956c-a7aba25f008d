import DatabaseManager from "./DatabaseManager.js"
import FileStorageManager from "./FileStorageManager.js"
import PoolManager from "./PoolManager.js"

export default class GachaService {
  constructor() {
    this.database = DatabaseManager.getInstance()
    this.fileStorage = new FileStorageManager()
    this.poolManager = new PoolManager()

    this.PITY_5STAR = 80
    this.PITY_4STAR = 10
    this.DAILY_LIMIT = 30

    this.PITY_STAGES = {
      base: { min: 1, max: 65, rate: 0.8 },
      stage1: { min: 66, max: 70, increase: 4 },
      stage2: { min: 71, max: 75, increase: 8 },
      stage3: { min: 76, max: 78, increase: 10 },
      guaranteed: { min: 79, rate: 100 },
    }
  }

  // 生成用户特征码
  async getUserCode(userId) {
    return await this.database.getUserCode(userId)
  }

  // 获取用户当前抽数（距离上次五星）
  async getCurrentDrawCount(userId, poolType) {
    const userCode = await this.getUserCode(userId)
    const normalizedPoolType = this.normalizePoolType(poolType)
    const poolData = await this.fileStorage.getPoolData(userCode, normalizedPoolType)
    return poolData.currentCount || 0
  }

  // 设置用户当前抽数
  async setCurrentDrawCount(userId, poolType, count) {
    const userCode = await this.getUserCode(userId)
    const normalizedPoolType = this.normalizePoolType(poolType)
    await this.fileStorage.updatePoolData(userCode, normalizedPoolType, { currentCount: count })
  }

  // 标准化池子类型
  normalizePoolType(poolType) {
    if (poolType.startsWith("limited_char")) {
      return "limited_char"
    } else if (poolType.startsWith("limited_weapon")) {
      return "limited_weapon"
    }
    return poolType
  }

  // 获取用户五星物品列表
  async getUserFiveStarList(userId) {
    const userCode = await this.getUserCode(userId)
    return await this.fileStorage.getFiveStarList(userCode)
  }

  // 添加五星物品到用户列表
  async addUserFiveStar(userId, item) {
    const userCode = await this.getUserCode(userId)

    const fiveStarItem = {
      name: item.name,
      type: item.type,
      isUp: item.isUp || false,
      poolType: item.poolType || "unknown",
      timestamp: Date.now(),
    }

    await this.fileStorage.addFiveStar(userCode, fiveStarItem)
  }

  // 添加抽卡记录
  async addGachaRecord(userId, poolType, item, pityCount) {
    const userCode = await this.getUserCode(userId)

    const record = {
      uid: userCode.toString(),
      gacha_id: this.getGachaId(poolType),
      gacha_type: this.getGachaType(poolType),
      item_id: item.id || '',
      count: '1',
      time: new Date().toISOString().replace('T', ' ').substring(0, 19),
      name: item.name,
      lang: 'zh-cn',
      item_type: item.type === 'character' ? '角色' : '武器',
      rank_type: item.rank.toString(),
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      pity_count: pityCount,
      is_up: item.isUp || false
    }

    await this.fileStorage.addGachaRecord(userCode, poolType, record)
  }

  // 获取抽卡记录
  async getGachaRecords(userId, poolType, limit = 100) {
    const userCode = await this.getUserCode(userId)
    return await this.fileStorage.getGachaRecords(userCode, poolType, limit)
  }

  // 获取五星抽卡记录
  async getFiveStarGachaRecords(userId, poolType = null) {
    const userCode = await this.getUserCode(userId)
    return await this.fileStorage.getFiveStarGachaRecords(userCode, poolType)
  }

  // 获取卡池ID映射
  getGachaId(poolType) {
    const mapping = {
      'limited_char': '301',
      'limited_weapon': '302',
      'standard_char': '200',
      'standard_weapon': '200'
    }
    return mapping[poolType] || '200'
  }

  // 获取卡池类型映射
  getGachaType(poolType) {
    const mapping = {
      'limited_char': '301',
      'limited_weapon': '302',
      'standard_char': '200',
      'standard_weapon': '200'
    }
    return mapping[poolType] || '200'
  }

  calculate5StarRate(pityCount) {
    if (pityCount >= this.PITY_STAGES.guaranteed.min) {
      return this.PITY_STAGES.guaranteed.rate
    }

    if (pityCount <= this.PITY_STAGES.base.max) {
      return this.PITY_STAGES.base.rate
    }

    let rate = this.PITY_STAGES.base.rate

    if (pityCount >= this.PITY_STAGES.stage1.min) {
      const stage1Count = Math.min(
        pityCount - this.PITY_STAGES.stage1.min + 1,
        this.PITY_STAGES.stage1.max - this.PITY_STAGES.stage1.min + 1,
      )
      rate += stage1Count * this.PITY_STAGES.stage1.increase
    }

    if (pityCount >= this.PITY_STAGES.stage2.min) {
      const stage2Count = Math.min(
        pityCount - this.PITY_STAGES.stage2.min + 1,
        this.PITY_STAGES.stage2.max - this.PITY_STAGES.stage2.min + 1,
      )
      rate += stage2Count * this.PITY_STAGES.stage2.increase
    }

    if (pityCount >= this.PITY_STAGES.stage3.min) {
      const stage3Count = Math.min(
        pityCount - this.PITY_STAGES.stage3.min + 1,
        this.PITY_STAGES.stage3.max - this.PITY_STAGES.stage3.min + 1,
      )
      rate += stage3Count * this.PITY_STAGES.stage3.increase
    }

    return rate
  }

  async performDraw(userId, pool, count) {
    const results = []
    let pityInfo = await this.getPityInfo(userId, pool.id)
    let currentCount = await this.getCurrentDrawCount(userId, pool.id)

    let finalPity4 = pityInfo.pity4
    let finalGuaranteed5 = pityInfo.guaranteed5
    let finalGuaranteed4 = pityInfo.guaranteed4
    let finalCurrentCount = currentCount

    for (let i = 0; i < count; i++) {
      let result

      const nextCount = currentCount + 1
      const currentPity4 = pityInfo.pity4 + 1
      const rate5star = this.calculate5StarRate(nextCount)

      if (rate5star >= 100) {
        result = this.draw5Star(pool, pityInfo.guaranteed5, pityInfo)
      } else if (currentPity4 >= this.PITY_4STAR) {
        result = this.draw4Star(pool, pityInfo.guaranteed4)
      } else {
        result = this.normalDraw(pool, pityInfo, nextCount)
      }

      result.userId = userId
      result.pityCount = nextCount
      result.probability = rate5star
      result.poolType = pool.id
      results.push(result)

      // 添加抽卡记录（所有星级）
      await this.addGachaRecord(userId, pool.id, {
        ...result,
        rank: result.star,
        id: result.id || '',
        type: result.type
      }, nextCount)

      if (result.star === 5) {
        // 添加到用户五星列表
        await this.addUserFiveStar(userId, result)

        await this.setLastFiveStar(userId, pool.id, {
          name: result.name,
          isUp: result.isUp,
          pityCount: nextCount,
          poolId: pool.id,
          poolName: pool.name,
          timestamp: Date.now(),
        })

        // 重置当前抽数
        currentCount = 0
        pityInfo.guaranteed5 = !result.isUp && pool.id.startsWith("limited_char")
        finalCurrentCount = 0
        finalGuaranteed5 = pityInfo.guaranteed5
      } else {
        currentCount = nextCount
        finalCurrentCount = nextCount
      }

      if (result.star === 4) {
        pityInfo.pity4 = 0
        pityInfo.guaranteed4 =
          !result.isUp &&
          (pool.id.startsWith("limited_char") || pool.id.startsWith("limited_weapon"))
        finalPity4 = 0
        finalGuaranteed4 = pityInfo.guaranteed4
      } else if (result.star === 3) {
        pityInfo.pity4++
        finalPity4++
      }
    }

    await this.updateDrawCount(userId, pool.id, count)
    await this.setCurrentDrawCount(userId, pool.id, finalCurrentCount)
    await this.updateFinalPity(
      userId,
      pool.id,
      finalCurrentCount,
      finalPity4,
      finalGuaranteed5,
      finalGuaranteed4,
    )

    return results
  }

  normalDraw(pool, pityInfo, currentCount) {
    const rate5star = this.calculate5StarRate(currentCount)
    const rate4star = 6.0 // 四星基础概率6%
    const rand = Math.random() * 100

    // 五星概率检查
    if (rand < rate5star) {
      return this.draw5Star(pool, pityInfo.guaranteed5, pityInfo)
    }

    // 四星概率检查（独立计算，不累加五星概率）
    if (rand < rate4star) {
      return this.draw4Star(pool, pityInfo.guaranteed4)
    }

    // 其余为三星
    return this.draw3Star(pool)
  }

  // 其他方法保持不变...
  getPoolGroup(poolId) {
    if (poolId.startsWith("limited_char")) {
      return "limited_char"
    } else if (poolId.startsWith("limited_weapon")) {
      return "limited_weapon"
    } else if (poolId === "standard_char" || poolId === "standard_weapon") {
      return "standard"
    }
    return poolId
  }

  async checkDrawLimit(userId, poolId, e) {
    return await this.checkGachaLimit(userId, poolId, e)
  }

  async checkGachaLimit(userId, poolId, e) {
    if (e.isMaster) {
      return true
    }

    const poolGroup = this.getPoolGroup(poolId)

    const todayCount = await this.getTodayGachaCount(userId, poolGroup)
    const dailyLimit = this.DAILY_LIMIT

    if (todayCount >= dailyLimit) {
      let message
      if (poolGroup === "limited_char") {
        message = `你今天在限定角色池已经抽了${todayCount}次，明天再来吧~`
      } else if (poolGroup === "limited_weapon") {
        message = `你今天在限定武器池已经抽了${todayCount}次，明天再来吧~`
      } else if (poolGroup === "standard") {
        message = `你今天在常驻池已经抽了${todayCount}次，明天再来吧~`
      } else {
        message = `你今天已经抽了${todayCount}次，明天再来吧~`
      }

      await e.reply(message)
      return false
    }

    return true
  }

  async getTodayGachaCount(userId, poolGroup) {
    const userCode = await this.getUserCode(userId)
    const dateKey = new Date().toISOString().split("T")[0]
    const dailyLimits = await this.fileStorage.getDailyLimits(userCode, dateKey)
    return dailyLimits[poolGroup] || 0
  }

  async getRemainingCount(userId, poolId) {
    const poolGroup = this.getPoolGroup(poolId)
    const todayCount = await this.getTodayGachaCount(userId, poolGroup)
    return Math.max(0, this.DAILY_LIMIT - todayCount)
  }

  async getUsedCount(userId, poolId) {
    const poolGroup = this.getPoolGroup(poolId)
    return await this.getTodayGachaCount(userId, poolGroup)
  }

  async updateDrawCount(userId, poolType, count) {
    const userCode = await this.getUserCode(userId)
    const dateKey = new Date().toISOString().split("T")[0]
    const poolGroup = this.getPoolGroup(poolType)
    const normalizedPoolType = this.normalizePoolType(poolType)

    // 更新每日计数
    const currentGroupCount = await this.getTodayGachaCount(userId, poolGroup)
    await this.fileStorage.updateDailyLimits(userCode, dateKey, poolGroup, currentGroupCount + count)

    // 更新总计数
    const poolData = await this.fileStorage.getPoolData(userCode, normalizedPoolType)
    const newTotalCount = (poolData.totalCount || 0) + count
    await this.fileStorage.updatePoolData(userCode, normalizedPoolType, { totalCount: newTotalCount })
  }

  async getTotalDrawCount(userId, poolType) {
    const userCode = await this.getUserCode(userId)
    const normalizedPoolType = this.normalizePoolType(poolType)
    const poolData = await this.fileStorage.getPoolData(userCode, normalizedPoolType)
    return poolData.totalCount || 0
  }

  async getSelectedPool(userId, poolType) {
    const userCode = await this.getUserCode(userId)
    const poolId = await this.fileStorage.getSelectedPool(userCode, poolType)
    if (!poolId) return null

    return await this.poolManager.getPoolById(poolId)
  }

  async setSelectedPool(userId, poolType, poolId) {
    const userCode = await this.getUserCode(userId)
    await this.fileStorage.setSelectedPool(userCode, poolType, poolId)
  }

  async getTargetWeapon(userId) {
    const userCode = await this.getUserCode(userId)
    return await this.fileStorage.getTargetWeapon(userCode)
  }

  async setTargetWeapon(userId, weapon) {
    const userCode = await this.getUserCode(userId)
    await this.fileStorage.setTargetWeapon(userCode, weapon)
  }

  async getPityInfo(userId, poolId) {
    const userCode = await this.getUserCode(userId)
    const normalizedPoolType = this.normalizePoolType(poolId)
    const poolData = await this.fileStorage.getPoolData(userCode, normalizedPoolType)

    const targetWeapon = poolId === "standard_weapon" ? await this.getTargetWeapon(userId) : null

    return {
      pity4: poolData.pity4 || 0,
      guaranteed5: poolData.guaranteed5 || false,
      guaranteed4: poolData.guaranteed4 || false,
      targetWeapon,
    }
  }

  async getLastFiveStar(userId, poolType) {
    const userCode = await this.getUserCode(userId)
    const normalizedPoolType = this.normalizePoolType(poolType)
    const poolData = await this.fileStorage.getPoolData(userCode, normalizedPoolType)
    return poolData.lastFiveStar || null
  }

  async setLastFiveStar(userId, poolType, info) {
    const userCode = await this.getUserCode(userId)
    const normalizedPoolType = this.normalizePoolType(poolType)
    await this.fileStorage.updatePoolData(userCode, normalizedPoolType, { lastFiveStar: info })
  }

  async updateFinalPity(userId, poolId, currentCount, pity4, guaranteed5, guaranteed4) {
    const userCode = await this.getUserCode(userId)
    const normalizedPoolType = this.normalizePoolType(poolId)

    await this.fileStorage.updatePoolData(userCode, normalizedPoolType, {
      pity4: pity4,
      guaranteed5: guaranteed5,
      guaranteed4: guaranteed4
    })
  }

  draw5Star(pool, guaranteed, pityInfo = {}) {
    if (pool.id === "standard_char") {
      const items = pool.characters["5star"]
      return {
        star: 5,
        name: items[Math.floor(Math.random() * items.length)],
        isUp: false,
        type: "character",
      }
    } else if (pool.id === "standard_weapon") {
      const targetWeapon = pityInfo.targetWeapon
      if (!targetWeapon) {
        const items = pool.weapons["5star"]
        return {
          star: 5,
          name: items[Math.floor(Math.random() * items.length)],
          isUp: false,
          type: "weapon",
        }
      }
      return {
        star: 5,
        name: targetWeapon,
        isUp: false,
        type: "weapon",
      }
    } else if (pool.id.startsWith("limited_char")) {
      // guaranteed为true表示上次歪了，这次必出UP
      if (guaranteed) {
        const upChar = pool.up["5star"][0]
        return {
          star: 5,
          name: upChar.name,
          isUp: true,
          type: "character",
        }
      } else {
        // 50%概率出UP，50%概率歪
        if (Math.random() < 0.5) {
          const upChar = pool.up["5star"][0]
          return {
            star: 5,
            name: upChar.name,
            isUp: true,
            type: "character",
          }
        } else {
          // 歪了，出常驻五星
          if (!pool.standard5star || pool.standard5star.length === 0) {
            // 如果没有配置常驻五星，则返回UP角色（兜底）
            const upChar = pool.up["5star"][0]
            return {
              star: 5,
              name: upChar.name,
              isUp: true,
              type: "character",
            }
          }
          const selected = pool.standard5star[Math.floor(Math.random() * pool.standard5star.length)]
          return {
            star: 5,
            name: selected,
            isUp: false,
            type: "character",
          }
        }
      }
    } else if (pool.id.startsWith("limited_weapon")) {
      // 限定武器池总是返回UP武器，没有歪的机制
      const upWeapon = pool.up["5star"][0]
      return {
        star: 5,
        name: upWeapon.name,
        isUp: true,
        type: "weapon",
      }
    }
  }

  draw4Star(pool, guaranteed) {
    if (pool.id === "standard_char") {
      const characterItems = pool.characters["4star"]
      const weaponItems = pool.weapons["4star"]

      const allItems = [
        ...characterItems.map(name => ({ name, type: "character" })),
        ...weaponItems.map(name => ({ name, type: "weapon" })),
      ]

      const selected = allItems[Math.floor(Math.random() * allItems.length)]
      return {
        star: 4,
        name: selected.name,
        isUp: false,
        type: selected.type,
      }
    } else if (pool.id === "standard_weapon") {
      const weaponItems = pool.weapons["4star"]
      const characterItems = pool.characters["4star"]

      const allItems = [
        ...weaponItems.map(name => ({ name, type: "weapon" })),
        ...characterItems.map(name => ({ name, type: "character" })),
      ]

      const selected = allItems[Math.floor(Math.random() * allItems.length)]
      return {
        star: 4,
        name: selected.name,
        isUp: false,
        type: selected.type,
      }
    } else if (pool.id.startsWith("limited_char")) {
      // guaranteed为true表示上次四星歪了，这次必出UP四星
      // 否则50%概率出UP四星，50%概率歪
      if (guaranteed || Math.random() < 0.5) {
        const items = pool.up["4star"]
        const selected = items[Math.floor(Math.random() * items.length)]
        return {
          star: 4,
          name: selected.name,
          isUp: true,
          type: selected.type,
        }
      } else {
        if (!pool.standard4star || pool.standard4star.length === 0) {
          return {
            star: 4,
            name: "未知角色",
            isUp: false,
            type: "character",
          }
        }

        const selected = pool.standard4star[Math.floor(Math.random() * pool.standard4star.length)]

        if (selected && typeof selected === "object" && selected.name) {
          return {
            star: 4,
            name: selected.name,
            isUp: false,
            type: selected.type,
          }
        } else {
          return {
            star: 4,
            name: selected || "未知角色",
            isUp: false,
            type: "character",
          }
        }
      }
    } else if (pool.id.startsWith("limited_weapon")) {
      if (guaranteed || Math.random() < 0.5) {
        const items = pool.up["4star"]
        const selected = items[Math.floor(Math.random() * items.length)]
        return {
          star: 4,
          name: selected.name,
          isUp: true,
          type: selected.type,
        }
      } else {
        if (!pool.standard4star || pool.standard4star.length === 0) {
          return {
            star: 4,
            name: "未知武器",
            isUp: false,
            type: "weapon",
          }
        }

        const selected = pool.standard4star[Math.floor(Math.random() * pool.standard4star.length)]

        if (selected && typeof selected === "object" && selected.name) {
          return {
            star: 4,
            name: selected.name,
            isUp: false,
            type: selected.type,
          }
        } else {
          return {
            star: 4,
            name: selected || "未知武器",
            isUp: false,
            type: "weapon",
          }
        }
      }
    }
  }

  draw3Star(pool) {
    const weapons = this.poolManager.get3StarWeapons()
    return {
      star: 3,
      name: weapons[Math.floor(Math.random() * weapons.length)],
      isUp: false,
      type: "weapon",
    }
  }
}
