import plugin from '../../../lib/plugins/plugin.js'
import GachaService from '../models/GachaService.js'
import PoolManager from '../models/PoolManager.js'

export class PoolSwitch extends plugin {
  constructor() {
    super({
      name: '鸣潮卡池切换',
      dsc: '切换限定卡池',
      event: 'message',
      priority: 5000,
      rule: [
        {
          reg: '^#?ww切换卡池(.*)$',
          fnc: 'switchPool'
        }
      ]
    })
    
    this.gachaService = new GachaService()
    this.poolManager = new PoolManager()
  }

  async switchPool() {
    const userId = this.e.user_id
    const poolName = this.e.msg.replace(/^#?ww切换卡池/, '').trim()
    
    try {
      const charPools = await this.poolManager.getActiveLimitedCharPools()
      const weaponPools = await this.poolManager.getActiveLimitedWeaponPools()
      
      if (poolName) {
        let targetPool = null
        let poolType = null
        
        targetPool = charPools.find(p => p.name.includes(poolName))
        if (targetPool) {
          poolType = 'limited_char'
        } else {
          targetPool = weaponPools.find(p => p.name.includes(poolName))
          if (targetPool) {
            poolType = 'limited_weapon'
          }
        }
        
        if (targetPool) {
          await this.gachaService.setSelectedPool(userId, poolType, targetPool.id)
          await this.reply(`已切换到【${targetPool.name}】\n保底次数将继承之前的记录`)
        } else {
          await this.reply('未找到指定的卡池，请检查名称是否正确')
        }
      } else {
        if (charPools.length <= 1 && weaponPools.length <= 1) {
          await this.reply('当前没有多个卡池可供切换')
          return
        }
        
        let msg = '当前可用的卡池：\n\n'
        
        if (charPools.length > 0) {
          msg += '【限定角色池】\n'
          charPools.forEach((pool, index) => {
            msg += `${index + 1}. ${pool.name}\n`
            msg += `   UP角色：${pool.up['5star'][0].name}\n`
          })
          msg += '\n'
        }
        
        if (weaponPools.length > 0) {
          msg += '【限定武器池】\n'
          weaponPools.forEach((pool, index) => {
            msg += `${charPools.length + index + 1}. ${pool.name}\n`
            msg += `   UP武器：${pool.up['5star'][0].name}\n`
          })
        }
        
        msg += '\n使用【#ww切换卡池+卡池名称】进行切换'
        await this.reply(msg)
      }
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 切换卡池失败：${error}`)
      await this.reply('切换卡池失败，请稍后再试')
    }
  }
}

export default PoolSwitch
