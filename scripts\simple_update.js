import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 根据现有数据，手动映射userCode到userId
// 这些数据可以从数据库或日志中获取
const userCodeToUserId = {
  // 这里需要根据实际情况填写
  // 100000000: 'actual_user_id_1',
  // 100000001: 'actual_user_id_2',
  // 等等...
}

async function updateUserDataFiles() {
  console.log('开始更新用户数据文件...')
  
  try {
    const dataDir = path.join(__dirname, '..', 'data')
    
    // 获取所有用户文件夹
    const userDirs = fs.readdirSync(dataDir).filter(item => {
      const fullPath = path.join(dataDir, item)
      return fs.statSync(fullPath).isDirectory() && /^\d+$/.test(item) && item !== 'db'
    })
    
    console.log(`找到 ${userDirs.length} 个用户文件夹`)
    
    for (const userCode of userDirs) {
      const userDataPath = path.join(dataDir, userCode, 'user_data.json')
      
      if (fs.existsSync(userDataPath)) {
        try {
          // 读取现有数据
          const userData = JSON.parse(fs.readFileSync(userDataPath, 'utf8'))
          
          // 获取对应的userId（如果有的话）
          const userId = userCodeToUserId[userCode] || null
          
          if (userId && userData.userId !== userId) {
            // 更新userId
            userData.userId = userId
            userData.lastUpdated = Date.now()
            
            // 保存更新后的数据
            fs.writeFileSync(userDataPath, JSON.stringify(userData, null, 2), 'utf8')
            console.log(`✅ 更新用户 ${userCode} 的userId: ${userId}`)
          } else {
            console.log(`ℹ️  用户 ${userCode} 保持userId为null（暂无对应的真实用户ID）`)
          }
        } catch (error) {
          console.error(`❌ 处理用户 ${userCode} 时出错:`, error)
        }
      } else {
        console.log(`⚠️  用户 ${userCode} 的user_data.json文件不存在`)
      }
    }
    
    console.log('用户数据文件更新完成！')
    console.log('注意：userId字段将在用户下次使用抽卡功能时自动设置为正确的值')
    
  } catch (error) {
    console.error('更新用户数据文件时出错:', error)
  }
}

// 运行更新脚本
updateUserDataFiles()
