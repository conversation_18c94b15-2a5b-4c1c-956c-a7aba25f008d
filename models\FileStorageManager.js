import { fileURLToPath } from 'node:url'
import path from 'node:path'
import fs from 'node:fs'

// 简单的logger实现，如果全局logger不存在的话
const logger = globalThis.logger || {
  info: console.log,
  error: console.error,
  warn: console.warn
}

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

export default class FileStorageManager {
  constructor() {
    this.dataDir = path.join(__dirname, '..', 'data')
    this.ensureDataDir()
  }

  ensureDataDir() {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true })
    }
  }

  // 确保用户目录存在
  ensureUserDir(userCode) {
    const userDir = path.join(this.dataDir, userCode.toString())
    if (!fs.existsSync(userDir)) {
      fs.mkdirSync(userDir, { recursive: true })
    }
    return userDir
  }

  // 获取用户数据文件路径
  getUserDataPath(userCode) {
    const userDir = this.ensureUserDir(userCode)
    return path.join(userDir, 'user_data.json')
  }

  // 获取用户数据
  async getUserData(userCode) {
    try {
      const filePath = this.getUserDataPath(userCode)

      // 从数据库获取userId
      const DatabaseManager = (await import('./DatabaseManager.js')).default
      const dbManager = DatabaseManager.getInstance()
      const userId = await dbManager.getUserIdByCode(userCode)

      if (!fs.existsSync(filePath)) {
        // 如果文件不存在，返回默认数据结构
        return this.getDefaultUserData(userCode, userId)
      }

      const data = fs.readFileSync(filePath, 'utf8')
      const userData = JSON.parse(data)

      // 确保数据结构完整，并设置正确的userId
      return this.validateAndFixUserData(userData, userCode, userId)
    } catch (error) {
      logger.error(`[鸣潮抽卡] 读取用户数据失败 (${userCode}):`, error)
      // 返回默认数据结构
      return this.getDefaultUserData(userCode)
    }
  }

  // 保存用户数据
  async saveUserData(userCode, userData) {
    try {
      const filePath = this.getUserDataPath(userCode)
      
      // 更新最后修改时间
      userData.lastUpdated = Date.now()
      
      // 写入文件
      fs.writeFileSync(filePath, JSON.stringify(userData, null, 2), 'utf8')
    } catch (error) {
      logger.error(`[鸣潮抽卡] 保存用户数据失败 (${userCode}):`, error)
      throw error
    }
  }

  // 获取默认用户数据结构
  getDefaultUserData(userCode, userId = null) {
    return {
      userCode: userCode,
      userId: userId,
      pools: {
        limited_char: {
          currentCount: 0,
          totalCount: 0,
          pity4: 0,
          guaranteed5: false,
          guaranteed4: false,
          lastFiveStar: null
        },
        limited_weapon: {
          currentCount: 0,
          totalCount: 0,
          pity4: 0,
          guaranteed5: false,
          guaranteed4: false,
          lastFiveStar: null
        },
        standard_char: {
          currentCount: 0,
          totalCount: 0,
          pity4: 0,
          guaranteed5: false,
          guaranteed4: false,
          lastFiveStar: null
        },
        standard_weapon: {
          currentCount: 0,
          totalCount: 0,
          pity4: 0,
          guaranteed5: false,
          guaranteed4: false,
          lastFiveStar: null,
          targetWeapon: null
        }
      },
      fiveStarList: [],
      gachaRecords: {
        limited_char: [],
        limited_weapon: [],
        standard_char: [],
        standard_weapon: []
      },
      dailyLimits: {},
      selectedPools: {},
      lastUpdated: Date.now()
    }
  }

  // 验证并修复用户数据结构
  validateAndFixUserData(userData, userCode, userId = null) {
    const defaultData = this.getDefaultUserData(userCode, userId)

    // 确保基本字段存在
    if (!userData.userCode) userData.userCode = userCode
    if (userId !== null) userData.userId = userId  // 设置正确的userId
    if (!userData.pools) userData.pools = defaultData.pools
    if (!userData.fiveStarList) userData.fiveStarList = []
    if (!userData.gachaRecords) userData.gachaRecords = defaultData.gachaRecords
    if (!userData.dailyLimits) userData.dailyLimits = {}
    if (!userData.selectedPools) userData.selectedPools = {}
    
    // 确保所有池子数据结构完整
    for (const poolType of ['limited_char', 'limited_weapon', 'standard_char', 'standard_weapon']) {
      if (!userData.pools[poolType]) {
        userData.pools[poolType] = defaultData.pools[poolType]
      } else {
        const pool = userData.pools[poolType]
        const defaultPool = defaultData.pools[poolType]
        
        // 确保所有必需字段存在
        for (const field in defaultPool) {
          if (pool[field] === undefined) {
            pool[field] = defaultPool[field]
          }
        }
      }
    }
    
    return userData
  }

  // 获取池子数据
  async getPoolData(userCode, poolType) {
    const userData = await this.getUserData(userCode)
    return userData.pools[poolType] || this.getDefaultUserData(userCode, null).pools[poolType]
  }

  // 更新池子数据
  async updatePoolData(userCode, poolType, poolData) {
    const userData = await this.getUserData(userCode)
    userData.pools[poolType] = { ...userData.pools[poolType], ...poolData }
    await this.saveUserData(userCode, userData)
  }

  // 获取每日限制数据
  async getDailyLimits(userCode, dateKey) {
    const userData = await this.getUserData(userCode)
    return userData.dailyLimits[dateKey] || {}
  }

  // 更新每日限制数据
  async updateDailyLimits(userCode, dateKey, poolGroup, count) {
    const userData = await this.getUserData(userCode)
    if (!userData.dailyLimits[dateKey]) {
      userData.dailyLimits[dateKey] = {}
    }
    userData.dailyLimits[dateKey][poolGroup] = count
    await this.saveUserData(userCode, userData)
  }

  // 添加五星物品
  async addFiveStar(userCode, item) {
    const userData = await this.getUserData(userCode)
    userData.fiveStarList.push(item)
    await this.saveUserData(userCode, userData)
  }

  // 获取五星列表
  async getFiveStarList(userCode) {
    const userData = await this.getUserData(userCode)
    return userData.fiveStarList || []
  }

  // 设置选择的池子
  async setSelectedPool(userCode, poolType, poolId) {
    const userData = await this.getUserData(userCode)
    userData.selectedPools[poolType] = poolId
    await this.saveUserData(userCode, userData)
  }

  // 获取选择的池子
  async getSelectedPool(userCode, poolType) {
    const userData = await this.getUserData(userCode)
    return userData.selectedPools[poolType] || null
  }

  // 添加抽卡记录（新的分文件存储方式）
  async addGachaRecord(userCode, poolType, record) {
    const userDir = this.ensureUserDir(userCode)
    const fileName = this.getGachaFileName(poolType)
    const filePath = path.join(userDir, fileName)

    // 读取现有记录
    let records = []
    if (fs.existsSync(filePath)) {
      try {
        const content = fs.readFileSync(filePath, 'utf8')
        records = JSON.parse(content)
      } catch (error) {
        logger.warn(`[鸣潮抽卡] 读取抽卡记录文件失败: ${filePath}`, error)
        records = []
      }
    }

    // 添加新记录
    records.push(record)

    // 保持记录数量在合理范围内（最多保留最近1000条）
    if (records.length > 1000) {
      records = records.slice(-1000)
    }

    // 保存记录
    fs.writeFileSync(filePath, JSON.stringify(records, null, 2), 'utf8')
  }

  // 获取抽卡记录文件名
  getGachaFileName(poolType) {
    const mapping = {
      'limited_char': 'limited_char.json',    // UP角色1、2
      'limited_weapon': 'limited_weapon.json', // UP武器1、2
      'standard_char': 'standard_char.json',   // 常驻角色
      'standard_weapon': 'standard_weapon.json' // 常驻武器
    }
    return mapping[poolType] || 'unknown.json'
  }

  // 获取抽卡记录（新的分文件存储方式）
  async getGachaRecords(userCode, poolType, limit = 100) {
    const userDir = this.ensureUserDir(userCode)
    const fileName = this.getGachaFileName(poolType)
    const filePath = path.join(userDir, fileName)

    if (!fs.existsSync(filePath)) {
      return []
    }

    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const records = JSON.parse(content)

      // 返回最近的记录，最新的在前面
      return records.slice(-limit).reverse()
    } catch (error) {
      logger.warn(`[鸣潮抽卡] 读取抽卡记录失败: ${filePath}`, error)
      return []
    }
  }

  // 获取所有抽卡记录（新的分文件存储方式）
  async getAllGachaRecords(userCode) {
    const poolTypes = ['limited_char', 'limited_weapon', 'standard_char', 'standard_weapon']
    const allRecords = {}

    for (const poolType of poolTypes) {
      allRecords[poolType] = await this.getGachaRecords(userCode, poolType, 1000)
    }

    return allRecords
  }

  // 获取五星抽卡记录（新的分文件存储方式）
  async getFiveStarGachaRecords(userCode, poolType = null) {
    let allFiveStars = []

    if (poolType) {
      // 获取指定池子的五星记录
      const records = await this.getGachaRecords(userCode, poolType, 1000)
      allFiveStars = records.filter(record => record.rank_type === '5')
    } else {
      // 获取所有池子的五星记录
      const poolTypes = ['limited_char', 'limited_weapon', 'standard_char', 'standard_weapon']
      for (const pool of poolTypes) {
        const records = await this.getGachaRecords(userCode, pool, 1000)
        const fiveStars = records.filter(record => record.rank_type === '5')
        allFiveStars.push(...fiveStars)
      }
    }

    // 按时间排序，最新的在前面
    return allFiveStars.sort((a, b) => new Date(b.time) - new Date(a.time))
  }

  // 设置目标武器
  async setTargetWeapon(userCode, weapon) {
    const userData = await this.getUserData(userCode)
    userData.pools.standard_weapon.targetWeapon = weapon
    await this.saveUserData(userCode, userData)
  }

  // 获取目标武器
  async getTargetWeapon(userCode) {
    const userData = await this.getUserData(userCode)
    return userData.pools.standard_weapon.targetWeapon || null
  }

  // 清理过期的每日限制数据（保留最近30天）
  async cleanupOldDailyLimits(userCode) {
    try {
      const userData = await this.getUserData(userCode)
      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      
      const cleanedLimits = {}
      for (const dateKey in userData.dailyLimits) {
        const date = new Date(dateKey)
        if (date >= thirtyDaysAgo) {
          cleanedLimits[dateKey] = userData.dailyLimits[dateKey]
        }
      }
      
      userData.dailyLimits = cleanedLimits
      await this.saveUserData(userCode, userData)
    } catch (error) {
      logger.error(`[鸣潮抽卡] 清理过期数据失败 (${userCode}):`, error)
    }
  }

  // 检查文件是否存在
  userDataExists(userCode) {
    const filePath = this.getUserDataPath(userCode)
    return fs.existsSync(filePath)
  }

  // 获取所有用户数据文件列表
  getAllUserDataFiles() {
    try {
      const files = fs.readdirSync(this.dataDir)
      return files.filter(file => file.endsWith('.json') && /^\d+\.json$/.test(file))
    } catch (error) {
      logger.error('[鸣潮抽卡] 获取用户数据文件列表失败:', error)
      return []
    }
  }
}
