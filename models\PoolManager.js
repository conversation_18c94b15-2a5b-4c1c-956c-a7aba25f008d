import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

export default class PoolManager {
  constructor() {
    const __filename = fileURLToPath(import.meta.url)
    const __dirname = path.dirname(__filename)
    this.configPath = path.join(path.resolve(__dirname, '..'), 'config')
    this.loadPools()
  }

  loadPools() {
    try {
      const limitedCharPath = path.join(this.configPath, 'current_limited_character.json')
      if (fs.existsSync(limitedCharPath)) {
        const content = fs.readFileSync(limitedCharPath, 'utf8')
        this.limitedCharPools = JSON.parse(content)
      } else {
        this.limitedCharPools = []
      }
      
      const limitedWeaponPath = path.join(this.configPath, 'current_limited_weapon.json')
      if (fs.existsSync(limitedWeaponPath)) {
        const content = fs.readFileSync(limitedWeaponPath, 'utf8')
        this.limitedWeaponPools = JSON.parse(content)
      } else {
        this.limitedWeaponPools = []
      }
      
      const standardPath = path.join(this.configPath, 'standard_pools.json')
      if (fs.existsSync(standardPath)) {
        const content = fs.readFileSync(standardPath, 'utf8')
        this.standardPools = JSON.parse(content)
      } else {
        this.standardPools = {}
      }
      
      const weapons3Path = path.join(this.configPath, 'weapons_3star.json')
      this.weapons3star = fs.existsSync(weapons3Path)
        ? JSON.parse(fs.readFileSync(weapons3Path, 'utf8'))
        : []
      
      const characters4Path = path.join(this.configPath, 'characters_4star.json')
      this.characters4star = fs.existsSync(characters4Path)
        ? JSON.parse(fs.readFileSync(characters4Path, 'utf8'))
        : []
      
      const weapons4Path = path.join(this.configPath, 'weapons_4star.json')
      this.weapons4star = fs.existsSync(weapons4Path)
        ? JSON.parse(fs.readFileSync(weapons4Path, 'utf8'))
        : []
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 加载配置文件失败: ${error}`)
    }
  }

  getActiveLimitedCharPools() {
    const activePools = this.limitedCharPools && this.limitedCharPools.filter(pool => {
      return pool && pool.active === true
    }) || []
    return activePools
  }

  getActiveLimitedWeaponPools() {
    const activePools = this.limitedWeaponPools && this.limitedWeaponPools.filter(pool => {
      return pool && pool.active === true
    }) || []
    return activePools
  }

  getStandardCharPool() {
    const pool = this.standardPools && (this.standardPools.character || this.standardPools.standard_char) || null
    return pool
  }

  getStandardWeaponPool() {
    const pool = this.standardPools && (this.standardPools.weapon || this.standardPools.standard_weapon) || null
    return pool
  }

  getLimitedCharacterPools() {
    return this.limitedCharPools || []
  }

  getLimitedWeaponPools() {
    return this.limitedWeaponPools || []
  }

  async getPoolById(poolId) {
    const charPool = this.limitedCharPools && this.limitedCharPools.find(pool => pool && pool.id === poolId)
    if (charPool) {
      return charPool
    }
    
    const weaponPool = this.limitedWeaponPools && this.limitedWeaponPools.find(pool => pool && pool.id === poolId)
    if (weaponPool) {
      return weaponPool
    }
    
    if (poolId === 'standard_char') {
      return this.getStandardCharPool()
    }
    
    if (poolId === 'standard_weapon') {
      return this.getStandardWeaponPool()
    }
    
    return null
  }

  getCurrentLimitedCharacterPools() {
    const now = new Date()
    const pools = this.limitedCharPools && this.limitedCharPools.filter(pool => {
      if (!pool || !pool.startTime || !pool.endTime) return false
      const start = new Date(pool.startTime)
      const end = new Date(pool.endTime)
      const isValid = now >= start && now <= end
      return isValid
    }) || []
    return pools
  }

  getCurrentLimitedWeaponPools() {
    const now = new Date()
    const pools = this.limitedWeaponPools && this.limitedWeaponPools.filter(pool => {
      if (!pool || !pool.startTime || !pool.endTime) return false
      const start = new Date(pool.startTime)
      const end = new Date(pool.endTime)
      const isValid = now >= start && now <= end
      return isValid
    }) || []
    return pools
  }

  getStandardCharacterPool() {
    return this.getStandardCharPool()
  }

  getStandardWeaponPool() {
    return this.getStandardWeaponPool()
  }

  get3StarWeapons() {
    return this.weapons3star || []
  }

  get4StarCharacters() {
    return this.characters4star || []
  }

  get4StarWeapons() {
    return this.weapons4star || []
  }

  async updateLimitedCharacterPools(pools) {
    try {
      const filePath = path.join(this.configPath, 'current_limited_character.json')
      fs.writeFileSync(filePath, JSON.stringify(pools, null, 2))
      this.limitedCharPools = pools
      return true
    } catch (error) {
      logger.error(`[鸣潮抽卡] 更新限定角色池失败: ${error}`)
      return false
    }
  }

  async updateLimitedWeaponPools(pools) {
    try {
      const filePath = path.join(this.configPath, 'current_limited_weapon.json')
      fs.writeFileSync(filePath, JSON.stringify(pools, null, 2))
      this.limitedWeaponPools = pools
      return true
    } catch (error) {
      logger.error(`[鸣潮抽卡] 更新限定武器池失败: ${error}`)
      return false
    }
  }

  isPoolExpired(pool) {
    if (!pool || !pool.endTime) return false
    const now = new Date()
    const end = new Date(pool.endTime)
    const expired = now > end
    return expired
  }

  async cleanExpiredPools() {
    const activeCharPools = this.limitedCharPools && this.limitedCharPools.filter(pool => !this.isPoolExpired(pool)) || []
    if (this.limitedCharPools && activeCharPools.length !== this.limitedCharPools.length) {
      await this.updateLimitedCharacterPools(activeCharPools)
    }
    
    const activeWeaponPools = this.limitedWeaponPools && this.limitedWeaponPools.filter(pool => !this.isPoolExpired(pool)) || []
    if (this.limitedWeaponPools && activeWeaponPools.length !== this.limitedWeaponPools.length) {
      await this.updateLimitedWeaponPools(activeWeaponPools)
    }
  }
}
