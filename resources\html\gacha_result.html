<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>鸣潮抽卡结果</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      background: linear-gradient(135deg, #0a0a1a, #1a1a2e, #16213e);
      color: white;
      margin: 0;
      padding: 20px;
      width: 800px;
      min-height: 400px;
      position: relative;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
      padding: 15px;
      background: rgba(0,0,0,0.4);
      border-radius: 12px;
      border: 1px solid rgba(255,255,255,0.1);
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    
    .pool-name {
      font-size: 26px;
      font-weight: bold;
      color: #f1c40f;
      margin-bottom: 8px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }
    
    .draw-type {
      font-size: 16px;
      color: #e74c3c;
      text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    }
    
    .items-grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 15px;
      margin-bottom: 20px;
    }
    
    .item {
      position: relative;
      width: 140px;
      height: 180px;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 6px 20px rgba(0,0,0,0.4);
      transition: transform 0.3s ease;
    }
    
    .item:hover {
      transform: translateY(-2px);
    }
    
    .item.star5 {
      background: linear-gradient(145deg, #b8860b, #ffd700, #ffed4e);
      box-shadow: 0 0 25px rgba(255, 215, 0, 0.6), 0 6px 20px rgba(0,0,0,0.4);
      border: 2px solid #ffd700;
    }
    
    .item.star4 {
      background: linear-gradient(145deg, #4a1070, #9d4edd, #c77dff);
      box-shadow: 0 0 20px rgba(157, 78, 221, 0.5), 0 6px 20px rgba(0,0,0,0.4);
      border: 2px solid #9d4edd;
    }
    
    .item.star3 {
      background: linear-gradient(145deg, #16378D, #4895ef, #74c0fc);
      box-shadow: 0 0 15px rgba(72, 149, 239, 0.4), 0 6px 20px rgba(0,0,0,0.4);
      border: 2px solid #4895ef;
    }
    
    .item-image {
      width: 120px;
      height: 120px;
      object-fit: contain;
      margin: 10px auto;
      display: block;
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
    }
    
    .item-info {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.7));
      padding: 10px 8px;
      text-align: center;
    }
    
    .item-name {
      font-size: 13px;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0,0,0,0.8);
    }
    
    .item-stars {
      color: #f1c40f;
      font-size: 14px;
      text-shadow: 0 1px 2px rgba(0,0,0,0.8);
      filter: drop-shadow(0 0 3px rgba(241, 196, 15, 0.5));
    }
    
    .up-mark {
      position: absolute;
      top: 8px;
      right: 8px;
      background: linear-gradient(145deg, #e74c3c, #ff6b6b);
      color: white;
      padding: 3px 8px;
      border-radius: 6px;
      font-size: 11px;
      font-weight: bold;
      text-shadow: 0 1px 2px rgba(0,0,0,0.5);
      box-shadow: 0 2px 6px rgba(231, 76, 60, 0.4);
    }
    
    /* 修改 footer 样式 */
    .footer {
      text-align: center;
      padding: 15px;
      padding-bottom: 30px; /* 确保内容不被绝对定位的特征码遮挡 */
      background: rgba(0,0,0,0.4);
      border-radius: 12px;
      font-size: 14px;
      color: #2ecc71;
      border: 1px solid rgba(255,255,255,0.1);
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      position: relative; /* 允许内部元素进行绝对定位 */
    }
    
    /* 修改 feature-code 样式 */
    .feature-code {
      position: absolute; /* 绝对定位，相对于父元素 .footer */
      bottom: 8px; /* 距离底部 8px */
      right: 8px; /* 距离右侧 8px */
      font-size: 12px; /* 关键修改：略微增大字体大小 */
      color: white; /* 白色字体 */
      font-weight: bold; /* 加粗 */
      font-family: 'Courier New', monospace;
      /* 关键修改：使用多层深色阴影，提供清晰度和立体感 */
      text-shadow: 
        0 0 1px black, /* 第一层：提供一个非常清晰的黑色轮廓 */
        0 0 3px rgba(0,0,0,0.8); /* 第二层：提供一个稍微扩散的深色光晕 */
      z-index: 1000;
    }
    
    /* 下方中间抽数展示 */
    .draw-count-display {
        font-size: 16px;
        color: #7efc00; /* 绿色，与 footer 的绿色协调 */
        text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    }
    
    /* 增强视觉效果 */
    .container::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255,255,255,0.03) 1px, transparent 1px);
      background-size: 20px 20px;
      pointer-events: none;
      z-index: -1;
    }
    
    /* 星级特效增强 */
    .item.star5 .item-stars {
      animation: goldGlow 2s ease-in-out infinite alternate;
    }
    
    @keyframes goldGlow {
      from { text-shadow: 0 1px 2px rgba(0,0,0,0.8), 0 0 5px rgba(241, 196, 15, 0.5); }
      to { text-shadow: 0 1px 2px rgba(0,0,0,0.8), 0 0 10px rgba(241, 196, 15, 0.8); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="pool-name">{{poolName}}</div>
      <div class="draw-type">{{drawType}}</div>
    </div>
    
    <div class="items-grid">
      {{each items item}}
      <div class="item star{{item.star}}">
        {{if item.isUp}}
        <div class="up-mark">UP</div>
        {{/if}}
        {{if item.imagePath}}
        <img class="item-image" 
             src="file://{{item.imagePath}}" 
             alt="{{item.name}}">
        {{else}}
        <div style="width: 120px; height: 120px; margin: 10px auto; display: flex; align-items: center; justify-content: center; background: rgba(0,0,0,0.3); border-radius: 4px; color: #ccc; font-size: 12px; text-align: center; padding: 5px;">
          {{item.name}}
        </div>
        {{/if}}
        <div class="item-info">
          <div class="item-name">{{item.name}}</div>
          <div class="item-stars">
            {{if item.star == 5}}★★★★★{{else if item.star == 4}}★★★★{{else}}★★★{{/if}}
          </div>
        </div>
      </div>
      {{/each}}
    </div>

    <div class="footer">
      <div class="draw-count-display">当前抽数：{{currentPity}} 抽</div>
      <div class="feature-code">特征码：{{userCode}}</div>
    </div>
  </div>
</body>
</html>
