import path from "node:path"
import fs from "node:fs"
import { fileURLToPath } from "node:url"

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const pluginPath = path.resolve(__dirname, "..")

function findCharacterImage(pluginPath, characterName) {
  const extensions = [".webp", ".png", ".jpg", ".jpeg"]
  const imagesDir = path.join(pluginPath, "resources", "images")

  if (!characterName || characterName === "undefined" || typeof characterName !== "string") {
    return null
  }

  const nameVariants = [
    characterName,
    characterName.replace(/[<>:"/\\|?*]/g, "").trim(),
    characterName.replace(/\s+/g, ""),
    characterName.replace(/·/g, ""),
  ]

  for (const name of nameVariants) {
    if (!name) continue

    for (const ext of extensions) {
      const imagePath = path.join(imagesDir, `${name}${ext}`)
      if (fs.existsSync(imagePath)) {
        return imagePath.replace(/\\/g, "/")
      }
    }
  }

  return null
}

function validateAndCleanResults(results) {
  if (!Array.isArray(results)) {
    return []
  }

  const validResults = []

  results.forEach((item, index) => {
    if (!item || typeof item !== "object") {
      console.warn(`[鸣潮抽卡] 跳过无效结果项 ${index}: 不是对象`, item)
      return
    }

    const name = item.name
    const star = item.star
    const type = item.type

    if (!name || name === "undefined" || typeof name !== "string") {
      console.warn(`[鸣潮抽卡] 跳过无效结果项 ${index}: 名称无效`, { name, star, type })
      return
    }

    if (!star || typeof star !== "number") {
      console.warn(`[鸣潮抽卡] 跳过无效结果项 ${index}: 星级无效`, { name, star, type })
      return
    }

    if (!type || typeof type !== "string") {
      console.warn(`[鸣潮抽卡] 跳过无效结果项 ${index}: 类型无效`, { name, star, type })
      return
    }

    const cleanItem = {
      name: String(name).trim(),
      star: Number(star),
      type: String(type).trim(),
      ...(item.isUp && { isUp: Boolean(item.isUp) }),
      ...(item.isNew && { isNew: Boolean(item.isNew) }),
      ...(item.pityCount && { pityCount: Number(item.pityCount) }),
      ...(item.probability && { probability: Number(item.probability) }),
    }

    validResults.push(cleanItem)
  })

  return validResults
}

// 使用waves-plugin模板的渲染函数
export async function renderGachaResult(results, pool, drawType, userCode, currentPity) {
  const validResults = validateAndCleanResults(results)

  if (validResults.length === 0) {
    throw new Error("没有有效的抽卡结果数据")
  }

  // 按星级排序，五星在前
  validResults.sort((a, b) => {
    if (a.star !== b.star) return b.star - a.star
    if (a.type !== b.type) return a.type === "character" ? -1 : 1
    return 0
  })

  // 处理结果数据，适配waves-plugin模板格式
  const processedResults = validResults.map((item, index) => {
    const imagePath = findCharacterImage(pluginPath, item.name)

    return {
      star: item.star,
      role: imagePath || "", // waves-plugin使用role字段存储角色图片路径
      name: item.name,
      type: item.type,
      isUp: item.isUp || false,
    }
  })

  // 使用waves-plugin的模板路径
  const templatePath = path.join(pluginPath, "resources", "Template", "simulatorGacha", "simulatorGacha.html")

  if (!fs.existsSync(templatePath)) {
    throw new Error(`模板文件不存在: ${templatePath}`)
  }

  // 构造waves-plugin模板需要的数据格式
  const gachaData = {
    userName: `特征码: ${userCode}` , // 只显示特征码
    poolName: pool?.name || "未知卡池",
    times: drawType === 10 ? "十连抽取" : "单抽",
    list: processedResults
  }

  const data = {
    gachaData: gachaData,
    pluginResources: path.join(pluginPath, "resources").replace(/\\/g, "/"), // 资源路径
    sys: {
      scale: 'style="transform: scale(1);"' // 缩放设置
    },
    copyright: '' // 不显示版权信息
  }


  try {
    // 确保 puppeteer 模块的导入路径正确
    const puppeteer = await import("../../../lib/puppeteer/puppeteer.js").then(m => m.default)

    const imgData = await puppeteer.screenshot("wwgacha", {
      tplFile: templatePath,
      ...data, // 将 data 对象展开，作为渲染的上下文数据
    })

    return imgData
  } catch (error) {
    console.error(`[鸣潮抽卡] 渲染图片失败: ${error.message}`)
    throw error
  }
}
