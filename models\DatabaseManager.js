import sqlite3 from 'sqlite3'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import fs from 'node:fs'
import { promisify } from 'node:util'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

class DatabaseManager {
  constructor() {
    this.dbPath = path.join(__dirname, '..', 'data', 'db', 'users.db')
    this.db = null
    this.initialized = false
  }

  static getInstance() {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  async init() {
    if (this.initialized) return

    try {
      // 确保data/db目录存在
      const dbDir = path.dirname(this.dbPath)
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true })
      }

      // 连接数据库
      this.db = new sqlite3.Database(this.dbPath)

      // 将回调函数转换为Promise
      this.dbRun = promisify(this.db.run.bind(this.db))
      this.dbGet = promisify(this.db.get.bind(this.db))
      this.dbAll = promisify(this.db.all.bind(this.db))

      // 创建表
      await this.createTables()

      this.initialized = true
      logger.info('[鸣潮抽卡] SQLite数据库初始化完成')
    } catch (error) {
      logger.error('[鸣潮抽卡] SQLite数据库初始化失败:', error)
      throw error
    }
  }

  async createTables() {
    try {
      // 创建用户特征码映射表
      await this.dbRun(`
        CREATE TABLE IF NOT EXISTS user_codes (
          user_id TEXT PRIMARY KEY,
          user_code INTEGER UNIQUE NOT NULL,
          created_at INTEGER NOT NULL
        )
      `)

      // 创建全局配置表
      await this.dbRun(`
        CREATE TABLE IF NOT EXISTS global_config (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL
        )
      `)

      // 创建索引
      await this.dbRun(`
        CREATE INDEX IF NOT EXISTS idx_user_code ON user_codes(user_code)
      `)
    } catch (error) {
      logger.error('[鸣潮抽卡] 创建数据库表失败:', error)
      throw error
    }
  }

  // 获取用户特征码
  async getUserCode(userId) {
    await this.init()

    try {
      const result = await this.dbGet('SELECT user_code FROM user_codes WHERE user_id = ?', [userId])

      if (result) {
        return result.user_code
      }

      // 如果不存在，创建新的特征码
      return await this.createUserCode(userId)
    } catch (error) {
      logger.error('[鸣潮抽卡] 获取用户特征码失败:', error)
      throw error
    }
  }

  // 创建新的用户特征码
  async createUserCode(userId) {
    await this.init()

    try {
      // 获取当前最大特征码
      let maxCode = await this.getGlobalConfig('max_user_code')
      if (!maxCode) {
        maxCode = 100000000
      } else {
        maxCode = parseInt(maxCode) + 1
      }

      // 插入新用户
      await this.dbRun(`
        INSERT INTO user_codes (user_id, user_code, created_at)
        VALUES (?, ?, ?)
      `, [userId, maxCode, Date.now()])

      // 更新最大特征码
      await this.setGlobalConfig('max_user_code', maxCode.toString())

      return maxCode
    } catch (error) {
      logger.error('[鸣潮抽卡] 创建用户特征码失败:', error)
      throw error
    }
  }

  // 根据特征码获取用户ID
  async getUserIdByCode(userCode) {
    await this.init()

    try {
      const result = await this.dbGet('SELECT user_id FROM user_codes WHERE user_code = ?', [userCode])
      return result ? result.user_id : null
    } catch (error) {
      logger.error('[鸣潮抽卡] 根据特征码获取用户ID失败:', error)
      throw error
    }
  }

  // 获取全局配置
  async getGlobalConfig(key) {
    await this.init()

    try {
      const result = await this.dbGet('SELECT value FROM global_config WHERE key = ?', [key])
      return result ? result.value : null
    } catch (error) {
      logger.error('[鸣潮抽卡] 获取全局配置失败:', error)
      throw error
    }
  }

  // 设置全局配置
  async setGlobalConfig(key, value) {
    await this.init()

    try {
      await this.dbRun(`
        INSERT OR REPLACE INTO global_config (key, value)
        VALUES (?, ?)
      `, [key, value])
    } catch (error) {
      logger.error('[鸣潮抽卡] 设置全局配置失败:', error)
      throw error
    }
  }

  // 获取所有用户特征码映射（用于迁移）
  async getAllUserCodes() {
    await this.init()

    try {
      return await this.dbAll('SELECT user_id, user_code FROM user_codes')
    } catch (error) {
      logger.error('[鸣潮抽卡] 获取所有用户特征码失败:', error)
      throw error
    }
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      this.db.close()
      this.db = null
      this.initialized = false
    }
  }
}

// 导出单例实例
export default DatabaseManager
