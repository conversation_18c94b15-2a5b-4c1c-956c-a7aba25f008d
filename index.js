import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import DatabaseManager from './models/DatabaseManager.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

logger.info('-----------------')
logger.info('【鸣潮抽卡插件】开始加载')

// 初始化数据库
try {
  const database = DatabaseManager.getInstance()
  await database.init()
  logger.info('【鸣潮抽卡插件】数据库初始化完成')
} catch (error) {
  logger.error('【鸣潮抽卡插件】数据库初始化失败:', error)
}

// 加载apps目录下的所有js文件
const files = fs.readdirSync(path.join(__dirname, 'apps')).filter(file => file.endsWith('.js'))

let ret = []
files.forEach((file) => {
    ret.push(import(`./apps/${file}`))
})

ret = await Promise.allSettled(ret)

let apps = {}
for (let i in files) {
    let name = files[i].replace('.js', '')

    if (ret[i].status != 'fulfilled') {
        logger.error(`载入插件错误：${logger.red(name)}`)
        logger.error(ret[i].reason)
        continue
    }
    apps[name] = ret[i].value[Object.keys(ret[i].value)[0]]
}

// 加载完成
logger.info('【鸣潮抽卡插件】加载完成')

export { apps }
