import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import FileStorageManager from '../models/FileStorageManager.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

async function testFileStorage() {
  console.log('开始测试FileStorageManager...')
  
  try {
    const storageManager = new FileStorageManager()
    
    // 测试用户代码
    const testUserCode = 999999999
    
    console.log('\n1. 测试getUserDataPath方法:')
    const userDataPath = storageManager.getUserDataPath(testUserCode)
    console.log(`用户数据路径: ${userDataPath}`)
    
    // 检查路径是否正确（应该在用户文件夹中）
    const expectedPath = path.join(process.cwd(), 'data', testUserCode.toString(), 'user_data.json')
    if (userDataPath === expectedPath) {
      console.log('✅ 路径格式正确')
    } else {
      console.log('❌ 路径格式错误')
      console.log(`期望: ${expectedPath}`)
      console.log(`实际: ${userDataPath}`)
    }
    
    console.log('\n2. 测试getUserData方法:')
    const userData = await storageManager.getUserData(testUserCode)
    console.log('用户数据结构:')
    console.log(`- userCode: ${userData.userCode}`)
    console.log(`- userId: ${userData.userId}`)
    console.log(`- pools: ${Object.keys(userData.pools).join(', ')}`)
    console.log(`- fiveStarList长度: ${userData.fiveStarList.length}`)
    
    console.log('\n3. 测试saveUserData方法:')
    userData.testField = 'test_value'
    userData.lastUpdated = Date.now()
    
    await storageManager.saveUserData(testUserCode, userData)
    console.log('✅ 用户数据保存成功')
    
    // 验证文件是否在正确位置
    const userDir = path.join(process.cwd(), 'data', testUserCode.toString())
    const userDataFile = path.join(userDir, 'user_data.json')
    
    if (fs.existsSync(userDataFile)) {
      console.log('✅ 文件保存在正确位置')
      
      // 读取并验证内容
      const savedData = JSON.parse(fs.readFileSync(userDataFile, 'utf8'))
      if (savedData.testField === 'test_value') {
        console.log('✅ 文件内容正确')
      } else {
        console.log('❌ 文件内容错误')
      }
    } else {
      console.log('❌ 文件未保存在正确位置')
    }
    
    console.log('\n4. 清理测试数据:')
    if (fs.existsSync(userDir)) {
      fs.rmSync(userDir, { recursive: true, force: true })
      console.log('✅ 测试数据清理完成')
    }
    
    console.log('\n✅ 所有测试完成！')
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error)
  }
}

// 运行测试
testFileStorage()
