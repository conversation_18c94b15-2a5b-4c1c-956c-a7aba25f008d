import plugin from '../../../lib/plugins/plugin.js'
import GachaService from '../models/GachaService.js'
import PoolManager from '../models/PoolManager.js'

export class WeaponTarget extends plugin {
  constructor() {
    super({
      name: '鸣潮定向武器',
      dsc: '设置常驻武器池定向',
      event: 'message',
      priority: 5000,
      rule: [
        {
          reg: '^#?ww定向武器(.*)$',
          fnc: 'setTargetWeapon'
        }
      ]
    })
    
    this.gachaService = new GachaService()
    this.poolManager = new PoolManager()
  }

  async setTargetWeapon() {
    const userId = this.e.user_id
    const weaponName = this.e.msg.replace(/^#?ww定向武器/, '').trim()
    
    try {
      const weaponPool = await this.poolManager.getStandardWeaponPool()
      if (!weaponPool || !weaponPool.weapons || !weaponPool.weapons['5star']) {
        await this.reply('常驻武器池配置错误，请联系管理员')
        return
      }
      
      const weapons5star = weaponPool.weapons['5star']
      
      if (weaponName) {
        const weapon = weapons5star.find(w => w.includes(weaponName))
        if (weapon) {
          await this.gachaService.setTargetWeapon(userId, weapon)
          await this.reply(`已将常驻武器池定向设置为【${weapon}】\n切换定向武器不会重置保底计数`)
        } else {
          await this.reply('未找到指定的武器，请检查名称是否正确')
        }
      } else {
        const currentTarget = await this.gachaService.getTargetWeapon(userId)
        
        let msg = '请选择要定向的5星武器：\n\n'
        weapons5star.forEach((weapon, index) => {
          const isCurrent = weapon === currentTarget ? ' (当前)' : ''
          msg += `${index + 1}. ${weapon}${isCurrent}\n`
        })
        msg += '\n使用【#ww定向武器+武器名称】进行设置'
        
        await this.reply(msg)
      }
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 设置定向武器失败：${error}`)
      await this.reply('设置定向武器失败，请稍后再试')
    }
  }
}

export default WeaponTarget
