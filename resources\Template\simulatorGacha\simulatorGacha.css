@font-face {
    font-family: "Guruwords";
    src: url("../../fonts/Guruwords.ttf");
    font-weight: normal;
    font-style: normal;
}

body {
    transform-origin: top left;
    width: 1920px;
    height: 1080px;
    margin: 0;
    font-family: "Guruwords";
}

body>div {
    width: 100%;
    height: 100%;
    background: url(imgs/background.png) no-repeat;
}

#gacha {
    display: grid;
    grid-template-columns: 265px 265px 265px 265px 265px;
    gap: 15px;
    margin: 0 auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.frame {
    position: relative;
    display: inline-block;
    margin: 10px;
}

.img-bottom {
    width: 100%;
    position: relative;

}

.img-role {
    width: 100%;
    position: absolute;
    left: 0px;
    bottom: 0px;
    object-fit: contain;
}

.img-cover {
    width: 100%;
    position: absolute;
    left: 0px;
    bottom: 0px;
}



#copyright {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 20px;
    font-weight: bold;
    color: white;
    text-shadow: 2px 2px 2px black;
    text-align: center;
}

#copyright .version {
    color: #d3bc8e;
    display: inline-block;
    padding: 0 3px;
}

.user-code {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: #ffffff;
    font-size: 20px;
    font-weight: 400;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}


