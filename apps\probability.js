import plugin from '../../../lib/plugins/plugin.js'
import GachaService from '../models/GachaService.js'
import PoolManager from '../models/PoolManager.js'

export class Probability extends plugin {
  constructor() {
    super({
      name: '鸣潮概率查询',
      dsc: '查询当前抽卡概率',
      event: 'message',
      priority: 5000,
      rule: [
        {
          reg: '^#?ww抽卡概率$',
          fnc: 'showProbability'
        },
        {
          reg: '^#?ww抽卡统计$',
          fnc: 'drawStatistics'
        }
      ]
    })
    
    this.gachaService = new GachaService()
    this.poolManager = new PoolManager()
  }

  async showProbability() {
    const userId = this.e.user_id
    
    try {
      let msg = '【鸣潮抽卡概率机制】\n\n'
      msg += '📊 5星概率递增机制：\n'
      msg += '• 1-65抽：0.8% (基础概率)\n'
      msg += '• 66-70抽：每抽+4% (4.8%→20.8%)\n'
      msg += '• 71-75抽：每抽+8% (28.8%→60.8%)\n'
      msg += '• 76-78抽：每抽+10% (70.8%→90.8%)\n'
      msg += '• 79抽：100% (硬保底)\n\n'
      
      msg += '📋 4星保底机制：\n'
      msg += '• 基础概率：6%\n'
      msg += '• 10抽必出4星或以上\n\n'
      
      msg += '━━━━━━━━━━━━━━━━━━\n'
      msg += '您的当前保底状态：\n\n'
      
      const charPools = await this.poolManager.getActiveLimitedCharPools()
      if (charPools.length > 0) {
        const selectedCharPool = await this.gachaService.getSelectedPool(userId, 'limited_char')
        const poolName = selectedCharPool ? selectedCharPool.name : charPools[0].name
        const pityInfo = await this.gachaService.getPityInfo(userId, 'limited_char')
        const currentCount = await this.gachaService.getCurrentDrawCount(userId, 'limited_char')
        const rate = this.gachaService.calculate5StarRate(currentCount + 1)
        
        msg += `🎭 限定角色池 [${poolName}]\n`
        msg += `• 已抽取：${currentCount}抽 (下抽${rate.toFixed(1)}%概率)`
        if (pityInfo.guaranteed5) msg += ' [大保底]'
        msg += '\n'
        msg += `• 4星：第${pityInfo.pity4 + 1}抽`
        if (pityInfo.guaranteed4) msg += ' [小保底]'
        msg += '\n\n'
      }
      
      const weaponPools = await this.poolManager.getActiveLimitedWeaponPools()
      if (weaponPools.length > 0) {
        const selectedWeaponPool = await this.gachaService.getSelectedPool(userId, 'limited_weapon')
        if (selectedWeaponPool) {
          const pityInfo = await this.gachaService.getPityInfo(userId, selectedWeaponPool.id)
          const currentCount = await this.gachaService.getCurrentDrawCount(userId, 'limited_weapon')
          const rate = this.gachaService.calculate5StarRate(currentCount + 1)
          
          msg += `⚔️ 限定武器池 [${selectedWeaponPool.name}]\n`
          msg += `• 已抽取：${currentCount}抽 (下抽${rate.toFixed(1)}%概率)\n`
          msg += `• 4星：第${pityInfo.pity4 + 1}抽`
          if (pityInfo.guaranteed4) msg += ' [小保底]'
          msg += '\n\n'
        }
      }
      
      const standardCharCount = await this.gachaService.getCurrentDrawCount(userId, 'standard_char')
      const standardCharInfo = await this.gachaService.getPityInfo(userId, 'standard_char')
      const standardWeaponCount = await this.gachaService.getCurrentDrawCount(userId, 'standard_weapon')
      const standardWeaponInfo = await this.gachaService.getPityInfo(userId, 'standard_weapon')
      
      msg += `🌟 常驻角色池\n`
      msg += `• 已抽取：${standardCharCount}抽 (下抽${this.gachaService.calculate5StarRate(standardCharCount + 1).toFixed(1)}%概率)\n`
      msg += `• 4星：第${standardCharInfo.pity4 + 1}抽\n\n`
      
      msg += `🗡️ 常驻武器池\n`
      const targetWeapon = await this.gachaService.getTargetWeapon(userId)
      if (targetWeapon) {
        msg += `• 定向：${targetWeapon}\n`
      } else {
        msg += `• 定向：未设置\n`
      }
      msg += `• 已抽取：${standardWeaponCount}抽 (下抽${this.gachaService.calculate5StarRate(standardWeaponCount + 1).toFixed(1)}%概率)\n`
      msg += `• 4星：第${standardWeaponInfo.pity4 + 1}抽\n`
      
      await this.reply(msg)
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 查询概率失败：${error}`)
      await this.reply('查询失败，请稍后再试')
    }
  }

  async drawStatistics() {
    const userId = this.e.user_id
    
    try {
      // 获取用户特征码
      const userCode = await this.gachaService.getUserCode(userId)
      
      let msg = `【抽卡统计】\n特征码：${userCode}\n\n`
      
      // 获取五星角色列表
      const fiveStarList = await this.gachaService.getUserFiveStarList(userId)
      
      if (fiveStarList.length > 0) {
        msg += '✨ 已获得五星：\n'
        fiveStarList.forEach((item, index) => {
          const typeMark = item.type === 'weapon' ? '🗡️' : '👤'
          const upMark = item.isUp ? ' [UP]' : ''
          msg += `${index + 1}. ${typeMark}${item.name}${upMark}\n`
        })
        msg += '\n'
      } else {
        msg += '✨ 暂未获得五星物品\n\n'
      }
      
      msg += '【今日抽卡次数】\n'
      
      const pools = [
        { type: 'limited_char', name: '限定角色池' },
        { type: 'limited_weapon', name: '限定武器池' },
        { type: 'standard_char', name: '常驻角色池' },
        { type: 'standard_weapon', name: '常驻武器池' }
      ]
      
      for (const pool of pools) {
        const used = await this.gachaService.getUsedCount(userId, pool.type)
        const remain = this.gachaService.DAILY_LIMIT - used
        msg += `${pool.name}：${used}/${this.gachaService.DAILY_LIMIT} (剩余${remain}次)\n`
      }
      
      msg += '\n每日凌晨4点重置抽卡次数'
      
      await this.reply(msg)
      
    } catch (error) {
      logger.error(`[鸣潮抽卡] 查询统计失败：${error}`)
      await this.reply('查询失败，请稍后再试')
    }
  }
}

export default Probability
