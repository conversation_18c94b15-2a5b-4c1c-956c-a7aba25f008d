import plugin from '../../../lib/plugins/plugin.js'
import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import schedule from 'node-schedule'
import PoolManager from '../models/PoolManager.js'

export class BannerUpdater extends plugin {
  constructor() {
    super({
      name: '鸣潮卡池更新',
      dsc: '自动更新鸣潮抽卡卡池数据',
      event: 'message',
      priority: 5000,
      rule: [
        {
          reg: '^#?ww更新卡池$',
          fnc: 'manualUpdate'
        },
        {
          reg: '^#?ww查看卡池$',
          fnc: 'checkCurrentPools'
        },
        {
          reg: '^#?ww下载卡池数据$',
          fnc: 'downloadRawData'
        }
      ]
    })
    
    const __filename = fileURLToPath(import.meta.url)
    const __dirname = path.dirname(__filename)
    this.configPath = path.join(path.resolve(__dirname, '..'), 'config')
    
    if (!fs.existsSync(this.configPath)) {
      fs.mkdirSync(this.configPath, { recursive: true })
    }
    
    this.poolManager = new PoolManager()
    
    this.itemTypeCache = {
      characters: new Set(),
      weapons: new Set()
    }
    
    this.setupScheduledTask()
  }
  
  setupScheduledTask() {
    const job = schedule.scheduleJob('0 12 * * *', async () => {
      logger.info('[鸣潮抽卡] 开始执行每日卡池数据更新...')
      await this.updateBanners()
    })
    
    logger.info('[鸣潮抽卡] 已设置每日中午12点自动更新卡池数据')
  }
  
  async manualUpdate() {
    try {
      const updated = await this.updateBanners()
      if (updated) {
        await this.reply('鸣潮卡池数据更新成功！')
      } else {
        await this.reply('鸣潮卡池数据更新失败，请查看日志')
      }
    } catch (error) {
      logger.error(`[鸣潮抽卡] 手动更新卡池失败：${error}`)
      await this.reply(`更新失败：${error.message}`)
    }
  }
  
  async checkCurrentPools() {
    try {
      this.poolManager.loadPools()
      
      const charPools = this.poolManager.getActiveLimitedCharPools()
      const weaponPools = this.poolManager.getActiveLimitedWeaponPools()
      
      let msg = '【当前激活的卡池】\n\n'
      
      if (charPools.length > 0) {
        msg += '📊 限定角色池：\n'
        charPools.forEach((pool, index) => {
          msg += `${index + 1}. ${pool.name}\n`
          msg += `   5★: ${pool.up['5star'].map(item => item.name).join(', ')}\n`
          msg += `   4★: ${pool.up['4star'].map(item => item.name).join(', ')}\n`
          msg += `   时间: ${pool.startTime} ~ ${pool.endTime}\n\n`
        })
      } else {
        msg += '📊 限定角色池：无激活的卡池\n\n'
      }
      
      if (weaponPools.length > 0) {
        msg += '🔫 限定武器池：\n'
        weaponPools.forEach((pool, index) => {
          msg += `${index + 1}. ${pool.name}\n`
          msg += `   5★: ${pool.up['5star'].map(item => item.name).join(', ')}\n`
          msg += `   4★: ${pool.up['4star'].map(item => item.name).join(', ')}\n`
          msg += `   时间: ${pool.startTime} ~ ${pool.endTime}\n\n`
        })
      } else {
        msg += '🔫 限定武器池：无激活的卡池\n\n'
      }
      
      msg += '下次自动更新时间：每日中午12:00'
      
      await this.reply(msg)
    } catch (error) {
      logger.error(`[鸣潮抽卡] 查看当前卡池失败：${error}`)
      await this.reply(`查看失败：${error.message}`)
    }
  }
  
  async downloadRawData() {
    try {
      const response = await fetch('https://top.camellya.xyz/api/waves/pool/list')
      const json = await response.json()
      
      if (!json.data || !Array.isArray(json.data)) {
        await this.reply('API返回数据格式错误')
        return
      }
      
      const filePath = path.join(this.configPath, 'raw_banner_data.json')
      fs.writeFileSync(filePath, JSON.stringify(json.data, null, 2))
      
      await this.reply(`已下载原始卡池数据，共 ${json.data.length} 个卡池\n保存路径：${filePath}`)
    } catch (error) {
      logger.error(`[鸣潮抽卡] 下载原始数据失败：${error}`)
      await this.reply(`下载失败：${error.message}`)
    }
  }
  
  parseDate(dateStr) {
    if (!dateStr || dateStr === '版本更新时间') return null
    
    const formats = [
      /(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/,
      /(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2})/,
      /(\d{4})\/(\d{2})\/(\d{2}) (\d{2}):(\d{2}):(\d{2})/,
      /(\d{4})\/(\d{2})\/(\d{2}) (\d{2}):(\d{2})/
    ]
    
    for (const format of formats) {
      const match = dateStr.match(format)
      if (match) {
        const [, year, month, day, hour, minute, second = 0] = match
        return new Date(
          parseInt(year), 
          parseInt(month) - 1,
          parseInt(day),
          parseInt(hour),
          parseInt(minute),
          parseInt(second)
        )
      }
    }
    
    const date = new Date(dateStr)
    if (!isNaN(date.getTime())) {
      return date
    }
    
    logger.warn(`[鸣潮抽卡] 无法解析日期: ${dateStr}`)
    return null
  }
  
  async updateBanners() {
    try {
      const response = await fetch('https://top.camellya.xyz/api/waves/pool/list')
      const json = await response.json()
      
      if (!json.data || !Array.isArray(json.data)) {
        logger.error('[鸣潮抽卡] API返回数据格式错误')
        return false
      }
      
      await this.savePoolsToFile(json.data, 'raw_banner_data.json')
      
      this.buildItemTypeMapping(json.data)
      
      const now = new Date()
      logger.info(`[鸣潮抽卡] 当前本地时间: ${this.formatDate(now)}`)
      
      const processedBanners = json.data.map(banner => {
        const endTime = this.parseDate(banner.end_time)
        
        let startTime
        if (banner.start_time === '版本更新时间') {
          startTime = this.findVersionUpdateStartTime(json.data, banner, now)
        } else {
          startTime = this.parseDate(banner.start_time)
        }
        
        const isActive = startTime && endTime && 
                        now >= startTime && 
                        now <= endTime
        
        return {
          ...banner,
          parsedStartTime: startTime,
          parsedEndTime: endTime,
          isActive
        }
      })
      
      const activeBanners = processedBanners.filter(banner => banner.isActive)
      
      logger.info(`[鸣潮抽卡] 当前活动卡池数量: ${activeBanners.length}`)
      activeBanners.forEach(banner => {
        logger.info(`[鸣潮抽卡] 活动卡池: ${banner.name || banner.title} (${banner.pool_type})`)
        logger.info(`[鸣潮抽卡] 时间: ${this.formatDate(banner.parsedStartTime)} ~ ${this.formatDate(banner.parsedEndTime)}`)
      })
      
      const characterBanners = activeBanners.filter(b => b.pool_type === '角色活动唤取')
      const weaponBanners = activeBanners.filter(b => b.pool_type === '武器活动唤取')
      
      const limitedCharPools = this.convertToLimitedPools(characterBanners, 'character')
      const limitedWeaponPools = this.convertToLimitedPools(weaponBanners, 'weapon')
      
      await this.savePoolsToFile(limitedCharPools, 'current_limited_character.json')
      await this.savePoolsToFile(limitedWeaponPools, 'current_limited_weapon.json')
      
      const standardPoolsPath = path.join(this.configPath, 'standard_pools.json')
      if (!fs.existsSync(standardPoolsPath)) {
        logger.info('[鸣潮抽卡] 未找到常驻卡池数据，请手动提供JSON文件')
      }
      
      this.poolManager.loadPools()
      
      logger.info(`[鸣潮抽卡] 卡池数据更新成功，当前角色池${limitedCharPools.length}个，武器池${limitedWeaponPools.length}个`)
      return true
    } catch (error) {
      logger.error(`[鸣潮抽卡] 更新卡池失败：${error}`)
      return false
    }
  }
  
  buildItemTypeMapping(banners) {
    this.itemTypeCache = {
      characters: new Set(),
      weapons: new Set()
    }
    
    banners.forEach(banner => {
      if (banner.pool_type === '角色活动唤取') {
        banner.five_star_names.forEach(name => {
          this.itemTypeCache.characters.add(name)
        })
      } else if (banner.pool_type === '武器活动唤取') {
        banner.five_star_names.forEach(name => {
          this.itemTypeCache.weapons.add(name)
        })
      }
    })
    
    const fourStarItemCounts = new Map()
    
    banners.forEach(banner => {
      banner.four_star_names.forEach(name => {
        if (!fourStarItemCounts.has(name)) {
          fourStarItemCounts.set(name, { character: 0, weapon: 0 })
        }
        
        const counts = fourStarItemCounts.get(name)
        if (banner.pool_type === '角色活动唤取') {
          counts.character++
        } else if (banner.pool_type === '武器活动唤取') {
          counts.weapon++
        }
      })
    })
    
    fourStarItemCounts.forEach((counts, name) => {
      if (this.itemTypeCache.characters.has(name) || this.itemTypeCache.weapons.has(name)) {
        return
      }
      
      if (counts.character > counts.weapon) {
        this.itemTypeCache.characters.add(name)
      } 
      else if (counts.weapon > counts.character) {
        this.itemTypeCache.weapons.add(name)
      }
      else if (counts.character > 0) {
        let classified = false
        
        for (const charName of this.itemTypeCache.characters) {
          if (this.shareSubstring(name, charName, 2)) {
            this.itemTypeCache.characters.add(name)
            classified = true
            break
          }
        }
        
        if (!classified) {
          for (const weaponName of this.itemTypeCache.weapons) {
            if (this.shareSubstring(name, weaponName, 2)) {
              this.itemTypeCache.weapons.add(name)
              classified = true
              break
            }
          }
        }
        
        if (!classified) {
          if (name.length >= 4) {
            this.itemTypeCache.weapons.add(name)
          } else {
            this.itemTypeCache.characters.add(name)
          }
        }
      }
    })
    
    logger.info(`[鸣潮抽卡] 自动识别了 ${this.itemTypeCache.characters.size} 个角色和 ${this.itemTypeCache.weapons.size} 个武器`)
  }
  
  shareSubstring(str1, str2, minLength) {
    for (let i = 0; i <= str1.length - minLength; i++) {
      const substr = str1.substring(i, i + minLength)
      if (str2.includes(substr)) {
        return true
      }
    }
    return false
  }
  
  findVersionUpdateStartTime(allBanners, currentBanner, now) {
    const sortedBanners = [...allBanners].sort((a, b) => {
      const aDate = this.parseDate(a.end_time)
      const bDate = this.parseDate(b.end_time)
      if (!aDate || !bDate) return 0
      return aDate.getTime() - bDate.getTime()
    })
    
    const sameBannerType = sortedBanners.filter(b => 
      b.pool_type === currentBanner.pool_type &&
      b.end_time !== currentBanner.end_time &&
      this.parseDate(b.end_time) < this.parseDate(currentBanner.end_time)
    )
    
    if (sameBannerType.length > 0) {
      const previousBanner = sameBannerType.reduce((latest, current) => {
        const latestDate = this.parseDate(latest.end_time)
        const currentDate = this.parseDate(current.end_time)
        if (!latestDate || !currentDate) return latest
        return currentDate > latestDate ? current : latest
      }, sameBannerType[0])
      
      if (previousBanner) {
        logger.info(`[鸣潮抽卡] 找到前一个卡池结束时间: ${previousBanner.end_time} 作为 ${currentBanner.name || currentBanner.title} 的开始时间`)
        return this.parseDate(previousBanner.end_time)
      }
    }
    
    logger.info(`[鸣潮抽卡] 未找到前一个卡池，使用当前时间作为 ${currentBanner.name || currentBanner.title} 的开始时间`)
    return now
  }
  
  convertToLimitedPools(banners, poolType) {
    const newPools = []
    
    banners.forEach((banner, index) => {
      if (!banner.five_star_names || banner.five_star_names.length === 0) {
        logger.warn(`[鸣潮抽卡] 卡池数据缺少5星物品: ${banner.name || banner.title}`)
        return
      }
      
      const mainItemName = banner.five_star_names[0]
      
      const poolName = `${mainItemName} · ${banner.title || banner.name}`
      
      const characters4star = this.poolManager.get4StarCharacters().map(name => ({
        name,
        type: 'character'
      }))
      
      const weapons4star = this.poolManager.get4StarWeapons().map(name => ({
        name,
        type: 'weapon'
      }))
      
      const pool = {
        id: `limited_${poolType}_${Date.now()}_${index}`,
        name: poolName,
        active: true,
        startTime: this.formatDate(banner.parsedStartTime),
        endTime: this.formatDate(banner.parsedEndTime),
        up: {
          '5star': banner.five_star_names.map(name => ({ 
            name, 
            type: this.getItemType(name)
          })),
          '4star': banner.four_star_names.map(name => ({ 
            name, 
            type: this.getItemType(name)
          }))
        },
        standard5star: [],
        standard4star: [...characters4star, ...weapons4star]
      }
      
      newPools.push(pool)
    })
    
    return newPools
  }
  
  getItemType(name) {
    if (this.itemTypeCache.characters.has(name)) {
      return 'character'
    } else if (this.itemTypeCache.weapons.has(name)) {
      return 'weapon'
    } else {
      logger.warn(`[鸣潮抽卡] 未知道具类型: ${name}，默认为武器`)
      return 'weapon'
    }
  }
  
  async savePoolsToFile(pools, filename) {
    try {
      const filePath = path.join(this.configPath, filename)
      fs.writeFileSync(filePath, JSON.stringify(pools, null, 2))
      return true
    } catch (error) {
      logger.error(`[鸣潮抽卡] 保存卡池文件失败：${error}`)
      return false
    }
  }
  
  formatDate(date) {
    if (!date) return '未知时间'
    
    try {
      const pad = (num) => String(num).padStart(2, '0')
      
      const year = date.getFullYear()
      const month = pad(date.getMonth() + 1)
      const day = pad(date.getDate())
      const hours = pad(date.getHours())
      const minutes = pad(date.getMinutes())
      const seconds = pad(date.getSeconds())
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    } catch (error) {
      logger.error(`[鸣潮抽卡] 格式化日期失败: ${error}`)
      return '日期错误'
    }
  }
}

export default BannerUpdater
